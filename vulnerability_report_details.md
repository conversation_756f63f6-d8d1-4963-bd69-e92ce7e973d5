# 🔐 Vietnam Notification Application - Vulnerability Findings

## 5.1 SQL Injection

### Impact
- An attacker can extract or manipulate all records from the user table, including names, email addresses, and password hashes.
- The attacker can identify which email addresses are valid on the platform by observing responses from /user/write.
- With enough information, an attacker could attempt to insert admin accounts, extract hashed passwords for offline cracking, or exploit the database server further.
- If the database has high privileges, the attack could be escalated to writing files or executing code on the server.

### Description
The application is vulnerable to both Classic SQL and Error based SQL injection , which allows attackers to inject malicious input into the parameter.

**Classic SQL Injection:**
The endpoint /user/index/1 takes user input in a search field (query) and directly includes it in a SQL query without any sanitization or validation. This allows attackers to craft malicious input that can change the meaning of the SQL query—commonly known as SQL Injection.

**Error SQL Injection:**
The /user/write endpoint returns full database error messages when an action fails (e.g., trying to create a user with an malicious input). These messages reveal sensitive technical details like table names, column structure, password hash and even the SQL query being executed, which helps attackers fine-tune their payloads.

### Recommendation
- Use parameterized queries or ORM methods to handle user input safely in all database queries, especially in /user/index/1.
- Implement strict validation on all input fields. If search functionality is required, escape or safely handle special characters like ', ", and --.
- Avoid exposing raw SQL errors to users. Return user-friendly error messages and log technical errors on the server side only.
- Prevent mass exploitation by implementing rate limiting and CAPTCHA on endpoints like /user/write.
- Regularly scan the application using automated and manual penetration testing to detect similar issues early in development.

---

## 5.2 Cross Site Scripting (XSS)

### Impact
- Attackers can steal session tokens and impersonate users, including admins.
- Users can be redirected to phishing pages or malware sites.
- Fake login prompts or input forms can be injected to collect user credentials.
- If exploited, it may expose sensitive data or enable attacks that violate security standards like OWASP or GDPR.

### Description
The application is vulnerable to both Reflected XSS and Stored XSS, which allows attackers to inject malicious JavaScript code into the web pages viewed by other users.

**Reflected XSS:**
This occurs when unvalidated user input is immediately reflected back in the HTTP response without proper sanitization. For example, injecting a script in a search or URL parameter results in that script being executed in the browser.

**Stored XSS:**
This is more dangerous and occurs when the malicious script is permanently stored on the server (e.g., in a user name or comment field) and is later displayed to other users when they load that page. In your case, the payload was inserted via the user management + button and triggered an SQL error that confirmed it was processed unsafely.

### Recommendation
- Always encode dynamic content in HTML, JavaScript, and attribute contexts before rendering. Use libraries like OWASP Java Encoder or framework-native functions.
- Strictly validate and whitelist expected input formats. Reject or escape dangerous characters like <, >, ", ', and &.
- Apply headers such as Content-Security-Policy (CSP), X-XSS-Protection, and X-Content-Type-Options to reduce the risk of XSS exploitation.
- Before saving any user-generated input (e.g., in stored XSS), validate and strip or neutralize script content.
- Regularly perform dynamic application security testing (DAST) to catch XSS vulnerabilities during development and after changes.

---

## 5.3 HTML Injection

### Impact
- An attacker can insert fake messages, alerts, or links that look legitimate.
- Users may be tricked into clicking on malicious links or fake forms injected into the page.
- If customers or internal users see defaced or misleading content, it could damage trust.
- In some cases, HTML injection can be chained with browser quirks or misconfigured CSP to execute JavaScript.

### Description
The application is vulnerable to HTML Injection, which means that user-supplied input is inserted into the page’s HTML structure without proper filtering or sanitization.

This allows an attacker to inject HTML elements such as:
- <b>, <h> for formatting,
- <img src=...>, <a href=...> for visual manipulation,
- or even <script> tags (if not filtered correctly, which can lead to XSS).

### Recommendation
- Use server-side libraries to remove or escape any HTML tags from user inputs unless they are explicitly required (e.g., in a rich text editor).
- Before rendering user-supplied data in HTML, always encode it based on the context (HTML, attributes, JavaScript).
- Add a Content-Security-Policy (CSP) to limit what types of scripts or content can be loaded or executed.
- Only allow expected formats and characters (e.g., names, emails) using regular expressions or strict field validations.
- Include HTML injection in your vulnerability scans and manual testing routines.

---

## 5.4 Lack of Rate Limiting (User Creation & Email)

### Impact
- Attackers can send hundreds or thousands of emails through your server, which could:
  - Flood internal mailboxes
  - Cause your domain to be blacklisted
  - Exhaust email server resources
  - Enable account creation abuse
- Automated scripts can mass-create fake or junk user accounts, affecting:
  - System performance and storage
  - Admin dashboard usability
  - Downstream processes (notifications, workflows, approvals)
  - Denial of Service (DoS)

### Description
The application does not enforce any restrictions on how many times a user can perform certain actions, such as:
- Creating new user accounts (via /user/write)
- Triggering email functionality

An attacker can automate repeated requests to abuse these functions—without hitting any block or delay. During testing, we were able to send multiple emails and repeatedly attempt user creation without being throttled or blocked. This indicates a lack of rate limiting, IP-based restrictions, or CAPTCHA validation on sensitive actions.

### Recommendation
- Apply per-IP and per-endpoint limits (e.g., 5 requests per minute).
- Use HTTP status 429 Too Many Requests for enforcement.
- Use CAPTCHA (like Google reCAPTCHA) for user creation and email-triggering actions to stop bots.
- Limit emails per user/IP within a time frame.
- Queue and monitor outgoing emails to detect abuse.
- Log repeated attempts and trigger alerts when thresholds are exceeded.
- Temporarily block or slow down suspicious behavior to reduce risk of automated abuse.

---

## 5.5 Directory Listing Enabled

### Impact
- Attackers can download backend files, view source code, or read configuration files that may contain hardcoded credentials, API keys, or DB connection strings.
- Makes it easier for attackers to fingerprint the backend technologies (e.g., Java, Spring Boot, database types, libraries used) and craft targeted exploits.
- Exposed .log files may reveal stack traces, SQL queries, session tokens, or internal server errors.
- Files like .bak, .old, .zip, or .tar may include sensitive or outdated data which should not be publicly accessible.

### Description
The web server is configured to allow directory listing, meaning that when users access certain URLs that point to a folder (rather than a specific file), the server returns an auto-generated list of all files and subdirectories inside that folder.

During testing, we discovered that directory listing is enabled on one or more paths. The exposed content included:
- Source code files
- Configuration files
- Database-related files
- Log and backup files
- Compiled Java .jar files and other technology indicators

This kind of exposure gives attackers a shortcut to understand the internal structure of your application and technologies used.

### Recommendation
- Disable Directory Listing.
- Block public access to .sql, .log, .env, .jar, and other sensitive extensions using server rules.
- Remove or relocate backup, temporary, and debug files from public web directories.
- Set up alerts for access to unusual file types or directory paths.
- Perform regular black-box scans to detect accidental exposure of server directories.

---

## 5.6 No SSL Certificate

### Impact
- Attackers on the same network (e.g., public Wi-Fi, internal LAN, or via compromised routers) can capture login credentials using tools like Wireshark or tcpdump.
- Without HTTPS, session cookies can also be intercepted and reused by attackers to hijack user sessions.
- The lack of encryption enables attackers to modify traffic in transit (inject scripts, change content, redirect users).
- This setup violates security best practices and may fail audits for standards like OWASP Top 10, ISO 27001, or GDPR.

### Description
The application transmits user credentials (username and password) over unencrypted HTTP (port 80) instead of secure HTTPS. During testing, it was observed that:
- Login requests are sent over http:// instead of https://
- There is no SSL/TLS certificate configured on the server
- Sensitive data like credentials are visible in plain text during transmission
- Port 80 is open and actively in use for serving traffic, with no redirection to HTTPS

This means all data between the user and the server—including login credentials—are sent in unencrypted form, which can be intercepted and read by any attacker monitoring the network.

### Recommendation
- Install a valid SSL/TLS certificate.
- Redirect all HTTP (port 80) traffic to HTTPS (port 443).
- Ensure all forms, login pages, and API requests send data over HTTPS only.
- Make sure base URLs and endpoints in frontend code and backend configs use https://.
- Use the HTTP Strict Transport Security (HSTS) header to force browsers to use HTTPS for all future visits.
- Once HTTPS redirection is confirmed, you may restrict or close port 80 to reduce attack surface.

---

## 5.7 Improper Error Handling

### Impact
- A verbose error message might reveal a SQL query with the user's input, exposing how the application constructs its queries. This information can be valuable for constructing SQL injection attacks.
- An error message might disclose the exact version of a web application framework, like Apache Struts. Attackers can then search for known vulnerabilities in that specific version.
- Revealing the full path of a file could allow an attacker to locate and potentially access sensitive configuration file.

### Description
An Improper Error Handling vulnerability occurs when an application, during an error state, reveals too much information about its internal workings, potentially exposing sensitive data or system details to attackers. This can include things like file paths, database connection strings, server configurations, stack traces, or even the specific versions of software being used. Attackers can leverage this information to launch targeted attacks, identify vulnerabilities, and gain unauthorized access.

### Recommendation
- Implement proper error handling in the application to prevent sensitive information from being included in error messages.
- Use custom error messages.

---

## 5.8 No CSRF Token

### Impact

- Attackers can trick logged-in users into performing unwanted actions (like changing a password or sending emails) just by clicking a link or visiting a malicious site.
- If an admin is tricked into clicking a malicious link, the attacker could perform admin-level actions.
- CSRF attacks require no visible interaction—the victim may not even realize anything has happened.

### Description

The application does not implement Cross-Site Request Forgery (CSRF) protection. This means that sensitive actions (like login, user creation, password changes, email sending, etc.) can be triggered without verifying the origin of the request.

Typically, web applications protect users by adding a unique CSRF token to each request. This token ensures that any submitted action truly originated from the legitimate user interface, not from an external or malicious source.

In your application, we observed that:

- Critical actions (e.g., POST requests to login or create users) do not contain any CSRF tokens
- The application does not validate whether a request was submitted from an authenticated session or a third-party site

### Recommendation

- Include a unique, unpredictable CSRF token in every form or state-changing request.
- Validate this token server-side before processing any action.
- If you're using frameworks like Laravel, Django, Spring, or ASP.NET, enable built-in CSRF protection.
- Ensure token validation is active on all sensitive endpoints.
- Validate the Origin and Referer headers to ensure requests come from your domain.
- Set cookies with SameSite=Strict or SameSite=Lax to prevent them from being sent in cross-site requests.
- Implement a Web Application Firewall.
- Harden HTTP Headers.

---

## 5.9 Host Header Injection

### Impact

- If the application uses the Host header to generate URLs in emails (e.g., password reset or account activation), an attacker can change the header to their domain and trick users into clicking malicious links.
- If a caching proxy or CDN stores responses based on the Host header, it may cache malicious content for all users.
- Some internal apps use Host-based routing. Manipulating the Host header may trick backend servers into thinking the request is meant for a different internal service.
- Attackers may craft phishing links using your domain, which undermines user trust and can damage your brand reputation.

### Description

The application does not properly validate or restrict the Host header in incoming HTTP requests. This allows an attacker to supply arbitrary or malicious values in the Host header and potentially manipulate how the server behaves.

For example, if a user sends this request:

```
GET / HTTP/1.1
Host: attacker.com
```

And the server reflects or trusts this header (e.g., in redirects, password reset links, or email verification URLs), the attacker may be able to:

- Change the base URL in system-generated links (like reset-password emails)
- Redirect users to malicious sites

### Recommendation

- Configure the web server or application to only accept requests with a valid Host header (e.g., notification-vn.loreal.wans).
- Drop or reject requests with unexpected or malformed Host values.
- Never build system-generated URLs (e.g., password reset links) based on Host header alone.
- Instead, use a fixed server name or derive it from a trusted configuration value.
- Force all requests to redirect to the expected host (e.g., always redirect to https\://notification-vn.loreal.wans) if the Host header is different.

---

## 5.10 Cross-Site Tracing (XST)

### Impact

- Attackers may be able to steal session cookies by abusing the TRACE method, especially if other vulnerabilities like XSS exist.
- If authentication headers (e.g., Basic Auth or Bearer tokens) are sent with the request, they may be exposed to the attacker.
- Even without direct exploitation, TRACE provides an attacker with insight into how the server handles headers and responses, which can help in building more complex attacks.
- Leaving TRACE enabled in production environments goes against secure configuration guidelines and may trigger audit or compliance failures.

### Description

The web server has the HTTP TRACE method enabled, which is typically used for diagnostic purposes. While not inherently dangerous by itself, if the TRACE method is left enabled in a production environment, it may introduce a security vulnerability called Cross-Site Tracing (XST).

### Recommendation

- Disable TRACE.
- Implement a Web Application Firewall.
- Harden HTTP Headers.

---

## 5.11 HTTP Verb Tampering

### Impact

- Attackers may use unsupported HTTP methods (e.g., HEAD, PUT, DELETE) to bypass security filters or perform actions that were assumed to be protected.
- For example, sending a DELETE request to an API endpoint that lacks method filtering could lead to unauthorized data deletion.
- Accepting unnecessary HTTP methods opens up more ways for attackers to probe or exploit the server.

### Description

HTTP Verb Tampering occurs when a web application fails to properly restrict or validate HTTP methods (also known as verbs) such as GET, POST, PUT, DELETE, OPTIONS, or PATCH.

During testing, it was found that the application or web server responds to unexpected or unsafe HTTP methods (like TRACE, DELETE, or OPTIONS) without proper handling or restriction.

### Recommendation

- Configure your web server or application to only allow necessary HTTP methods (usually GET and POST).
- Block or return a 405 Method Not Allowed response for all others.
- Ensure that authentication and authorization checks apply regardless of HTTP method used.
- Implement logic in your application to reject or handle unexpected HTTP methods explicitly.

---

## 5.12 Web Server Fingerprinting (Banner Grabbing)

### Impact
- Attackers can identify specific server software and versions, then look up public exploits or vulnerabilities targeting those components.
- If any of the versions disclosed are outdated or vulnerable (e.g., Apache 2.4.51 has known issues), attackers may use this data to tailor their attacks more effectively.
- This contributes to the reconnaissance phase of an attack, giving attackers unnecessary insights into backend architecture.

### Description
The web server responds to client requests with detailed information about the underlying technologies in use. This is commonly known as "banner grabbing", where the Server header in HTTP responses exposes the exact versions of:
- Apache 2.4.51
- PHP 7.3.33
- OpenSSL 1.1.1l
- Nginx 1.22.1

This information is automatically included in HTTP response headers by default configurations. While not directly harmful on its own, exposing such precise version data helps attackers fingerprint your technology stack and search for known vulnerabilities associated with those versions.

### Recommendation
- Suppress or modify the Server header.
- Apache:
  - `ServerSignature Off`
  - `ServerTokens Prod`
- Nginx:
  - `server_tokens off` in `nginx.conf`
- PHP:
  - Disable version disclosure: `expose_php = Off`
- Keep Apache, Nginx, PHP, and OpenSSL up to date.

---

## 5.13 Vulnerable and Outdated Components (JavaScript Libraries)

### Impact
- Vulnerable jQuery and Bootstrap methods can be used by attackers to inject malicious scripts that run in the user's browser.
- Attackers can manipulate DOM operations, intercept user interactions, or perform clickjacking-like attacks.
- Exploiting a library flaw to deface the UI or steal information can damage user trust and brand reputation.

### Description
The application is using outdated third-party JavaScript libraries, specifically:
- Bootstrap v3.2.0
- jQuery v2.1.3

These libraries are known to have multiple security vulnerabilities, performance issues, and are no longer actively maintained. Attackers can exploit known issues in these versions to compromise client-side security.

For example:
- Bootstrap 3.2.0 has known XSS vulnerabilities due to unsafe data binding and HTML injection.
- jQuery 2.1.3 is vulnerable to Cross-Site Scripting (XSS) via specially crafted input passed to jQuery selectors, especially when using `html()` or `append()` functions.

### Recommendation
- jQuery: Upgrade to at least v3.6.0 or the latest stable release.
- Bootstrap: Upgrade to v5.x, or at least the latest patch version of v3.
- Use tools like Retire.js, Snyk, or npm audit to identify outdated or vulnerable libraries.
- Deploy a CSP to reduce impact of client-side vulnerabilities.
- Avoid using `eval()`, `document.write()`, and dynamic script injections.

---

## 5.14 Potentially Vulnerable to Slowloris DoS Attack

### Impact
- Attackers can exhaust server resources by maintaining numerous open, incomplete connections, leading to unavailability for legitimate users.
- Slowloris uses minimal bandwidth and appears as slow, legitimate traffic, making it hard to detect and block.

### Description
The Slowloris attack is a type of Denial of Service (DoS) attack that targets web servers by opening multiple HTTP connections and keeping them alive as long as possible. It works by sending partial HTTP requests very slowly, preventing the server from closing the connections and thereby exhausting its resources.

### Recommendation
- Set strict timeout values for idle or incomplete connections.
- Restrict the number of simultaneous connections per IP.
- Ensure your web server (Apache, Nginx) is up-to-date and configured to mitigate slow connection attacks.
- Use a Web Application Firewall (WAF) to detect and block slow/suspicious traffic.
- Enable `mod_reqtimeout` (Apache) or `client_body_timeout` (Nginx).
- Apply rate limiting at network or application level.

---

## 5.15 Missing Security Headers

### Impact
- Without CSP and X-XSS-Protection, the browser lacks directives to prevent or mitigate XSS attacks.
- Without X-Frame-Options, attackers can embed your application in iframes for clickjacking.
- Without X-Content-Type-Options, browsers may misinterpret MIME types.
- Without Referrer-Policy, sensitive URLs may leak when navigating to other sites.
- Without HSTS, attackers can force HTTP connections and conduct man-in-the-middle attacks.

### Description
The application does not include several important HTTP security headers that are designed to protect users and browsers from common web-based attacks. These headers instruct the browser on how to handle content, loading behavior, framing, and data sharing.

Missing headers:
- Content-Security-Policy (CSP)
- X-Frame-Options
- X-Content-Type-Options
- X-XSS-Protection
- Strict-Transport-Security (HSTS)
- Referrer-Policy

### Recommendation
Implement the following HTTP headers in all server responses:
- Content-Security-Policy (CSP)
- X-Frame-Options
- X-Content-Type-Options
- X-XSS-Protection
- Strict-Transport-Security (HSTS)
- Referrer-Policy

---
